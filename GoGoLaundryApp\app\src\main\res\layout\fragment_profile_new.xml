<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Profile Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="24dp"
            android:background="@color/white">

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/profile_image"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:padding="2dp"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_person"
                app:shapeAppearanceOverlay="@style/CircleImageView"
                app:strokeColor="?attr/colorPrimary"
                app:strokeWidth="2dp" />

            <TextView
                android:id="@+id/profile_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="test"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary" />

            <ImageView
                android:id="@+id/edit_profile_image"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/circle_background"
                android:padding="8dp"
                android:src="@drawable/ic_edit"
                app:tint="@android:color/white" />
        </LinearLayout>

        <!-- Menu Items -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Profile Update Menu Item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/profile_update_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/update_profile"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/primary" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Password Change Menu Item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/password_change_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/change_password"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/primary" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Contact Menu Item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/contact_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/contact_for_ads"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/primary" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Facebook Page Menu Item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/facebook_page_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/facebook_page"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/primary" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Facebook Group Menu Item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/facebook_group_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/facebook_group"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/primary" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- About App Menu Item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/about_app_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/about_app"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/primary" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <!-- Logout Menu Item -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/logout_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/logout_button"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp" />

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_arrow_right"
                        app:tint="@color/primary" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>

        <!-- Version Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="16dp">

            <TextView
                android:id="@+id/app_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Version 1.0.0"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
