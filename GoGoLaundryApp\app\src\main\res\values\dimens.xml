<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Margin and padding -->
    <dimen name="margin_extra_small">4dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_extra_large">32dp</dimen>
    <dimen name="margin_huge">48dp</dimen>

    <!-- Text sizes -->
    <dimen name="text_size_micro">12sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_extra_large">20sp</dimen>
    <dimen name="text_size_huge">24sp</dimen>
    <dimen name="text_size_title">22sp</dimen>
    <dimen name="text_size_headline">26sp</dimen>

    <!-- Elevation -->
    <dimen name="elevation_card">2dp</dimen>
    <dimen name="elevation_card_small">1dp</dimen>
    <dimen name="elevation_fab">6dp</dimen>
    <dimen name="elevation_appbar">4dp</dimen>
    <dimen name="elevation_dialog">24dp</dimen>

    <!-- Component specific -->
    <dimen name="button_height">56dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="card_corner_radius">8dp</dimen>
    <dimen name="input_field_height">56dp</dimen>
    <dimen name="divider_height">1dp</dimen>
    <dimen name="icon_size_small">24dp</dimen>
    <dimen name="icon_size_medium">36dp</dimen>
    <dimen name="icon_size_large">48dp</dimen>
    <dimen name="avatar_size">40dp</dimen>
    <dimen name="toolbar_height">56dp</dimen>

    <!-- Additional dimensions for date and time pickers -->
    <dimen name="card_corner_radius_small">4dp</dimen>
    <dimen name="date_picker_header_height">72dp</dimen>
    <dimen name="time_picker_height">256dp</dimen>

    <!-- Service card responsive dimensions -->
    <dimen name="service_card_min_height">180dp</dimen>
    <dimen name="service_card_max_height">220dp</dimen>
    <dimen name="service_icon_container_size">80dp</dimen>
    <dimen name="service_icon_size">48dp</dimen>
    <dimen name="service_card_corner_radius">16dp</dimen>
    <dimen name="service_card_elevation">4dp</dimen>
    <dimen name="grid_spacing">8dp</dimen>

    <!-- Compact service card dimensions -->
    <dimen name="compact_service_image_height">120dp</dimen>
    <dimen name="compact_service_card_padding">12dp</dimen>
    <dimen name="compact_service_image_margin">16dp</dimen>

    <!-- Shop Map dimensions -->
    <dimen name="star_size">16dp</dimen>
    <dimen name="star_margin">2dp</dimen>
    <dimen name="shop_card_margin">8dp</dimen>
    <dimen name="shop_image_size">60dp</dimen>
    <dimen name="shop_profile_image_size">80dp</dimen>
    <dimen name="map_search_margin">16dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="bottom_sheet_handle_width">40dp</dimen>
    <dimen name="bottom_sheet_handle_height">4dp</dimen>
</resources>
