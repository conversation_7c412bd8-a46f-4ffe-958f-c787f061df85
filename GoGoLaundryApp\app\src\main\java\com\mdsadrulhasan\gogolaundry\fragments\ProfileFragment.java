package com.mdsadrulhasan.gogolaundry.fragments;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.fragment.app.Fragment;


import com.bumptech.glide.Glide;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.textfield.TextInputEditText;
import com.google.gson.Gson;
import com.mdsadrulhasan.gogolaundry.LoginActivity;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.api.UserResponse;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.utils.DialogUtils;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;

import cn.pedant.SweetAlert.SweetAlertDialog;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class ProfileFragment extends Fragment {

    private static final int REQUEST_IMAGE_CAPTURE = 1;
    private static final int REQUEST_GALLERY_IMAGE = 2;
    private static final int PERMISSION_REQUEST_CAMERA = 100;
    private static final int PERMISSION_REQUEST_STORAGE = 101;

    private ShapeableImageView profileImage;
    private View editProfileImage; // Changed from ImageView to View since it's now a FrameLayout
    private TextInputEditText fullNameEditText;
    private TextInputEditText phoneEditText;
    private TextInputEditText emailEditText;
    private TextInputEditText addressEditText;
    private MaterialButton saveButton;
    private MaterialButton changePasswordButton;
    private View rootView; // Root view for finding views

    private SessionManager sessionManager;
    private ApiService apiService;
    private User currentUser;
    private Uri photoUri;
    private File photoFile;
    private SweetAlertDialog loadingDialog;

    // Permission launchers
    private ActivityResultLauncher<String[]> cameraPermissionLauncher;
    private ActivityResultLauncher<String[]> storagePermissionLauncher;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Initialize permission launchers
        cameraPermissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestMultiplePermissions(),
                permissions -> {
                    boolean allGranted = true;
                    for (Boolean granted : permissions.values()) {
                        if (!granted) {
                            allGranted = false;
                            break;
                        }
                    }

                    if (allGranted) {
                        // All permissions granted, proceed with camera
                        openCamera();
                    } else {
                        DialogUtils.showWarningDialog(requireContext(),
                            getString(R.string.permission_required),
                            getString(R.string.camera_permission_required));
                    }
                });

        storagePermissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestMultiplePermissions(),
                permissions -> {
                    boolean allGranted = true;
                    for (Boolean granted : permissions.values()) {
                        if (!granted) {
                            allGranted = false;
                            break;
                        }
                    }

                    if (allGranted) {
                        // All permissions granted, proceed with gallery
                        openGallery();
                    } else {
                        DialogUtils.showWarningDialog(requireContext(),
                            getString(R.string.permission_required),
                            getString(R.string.storage_permission_required));
                    }
                });

        // Log session information for debugging
        SessionManager sessionManager = new SessionManager(requireContext());
        Log.d("ProfileFragment", "Session cookies: " + sessionManager.getCookies());
        Log.d("ProfileFragment", "User logged in: " + sessionManager.isLoggedIn());
        if (sessionManager.getUser() != null) {
            Log.d("ProfileFragment", "User ID: " + sessionManager.getUser().getId());
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        rootView = inflater.inflate(R.layout.fragment_profile, container, false);

        // Initialize session manager and API service
        sessionManager = new SessionManager(requireContext());
        apiService = ApiClient.getApiService(requireContext());
        currentUser = sessionManager.getUser();

        // Initialize views
        initViews(rootView);

        // Set up click listeners
        setupClickListeners();

        // Populate user data
        populateUserData();

        return rootView;
    }

    private void initViews(View view) {
        // Profile image and edit button
        profileImage = view.findViewById(R.id.profile_image);
        editProfileImage = view.findViewById(R.id.edit_profile_image);

        // Form fields
        fullNameEditText = view.findViewById(R.id.full_name_edit_text);
        phoneEditText = view.findViewById(R.id.phone_edit_text);
        emailEditText = view.findViewById(R.id.email_edit_text);
        addressEditText = view.findViewById(R.id.address_edit_text);

        // Buttons
        saveButton = view.findViewById(R.id.save_button);
        changePasswordButton = view.findViewById(R.id.change_password_button);

        // User name display (new field in header)
        TextView userNameDisplay = view.findViewById(R.id.user_name_display);
        if (currentUser != null && userNameDisplay != null) {
            userNameDisplay.setText(currentUser.getFullName());
        }

        // Statistics views (optional - can be updated later with real data)
        TextView totalOrdersCount = view.findViewById(R.id.total_orders_count);
        TextView totalSpentAmount = view.findViewById(R.id.total_spent_amount);
        TextView memberSinceDate = view.findViewById(R.id.member_since_date);

        // Set default values for now
        if (totalOrdersCount != null) totalOrdersCount.setText("0");
        if (totalSpentAmount != null) totalSpentAmount.setText("৳0");
        if (memberSinceDate != null) memberSinceDate.setText("2024");
    }

    private void setupClickListeners() {
        // Edit profile image
        if (editProfileImage != null) {
            editProfileImage.setOnClickListener(v -> showImageSelectionDialog());
        }

        // Save button
        if (saveButton != null) {
            saveButton.setOnClickListener(v -> saveProfileChanges());
        }

        // Change password button
        if (changePasswordButton != null) {
            changePasswordButton.setOnClickListener(v -> {
                // Navigate to password change screen
                DialogUtils.showInfoDialog(requireContext(),
                    getString(R.string.coming_soon),
                    getString(R.string.change_password_coming_soon));
            });
        }

        // Logout button
        MaterialButton logoutButton = rootView.findViewById(R.id.logout_button);
        if (logoutButton != null) {
            logoutButton.setOnClickListener(v -> {
                // Show confirmation dialog for logout
                DialogUtils.showConfirmationDialog(requireContext(),
                    "Logout",
                    "Are you sure you want to logout?",
                    sweetAlertDialog -> {
                        // Perform logout
                        sweetAlertDialog.dismissWithAnimation();
                        sessionManager.logout();
                        // Navigate to login screen or restart app
                        requireActivity().finish();
                    },
                    sweetAlertDialog -> {
                        // Cancel logout
                        sweetAlertDialog.dismissWithAnimation();
                    });
            });
        }

    }

    private void logout() {
        // Clear session
        sessionManager.logout();

        // Navigate to login screen
        Intent intent = new Intent(requireContext(), LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        requireActivity().finish();
    }

    private void populateUserData() {
        if (currentUser != null) {
            // Set profile image if available
            if (currentUser.getProfilePictureUrl() != null && !currentUser.getProfilePictureUrl().isEmpty()) {
                String baseUrl = ApiClient.getBaseUrl();
                String imageUrl = baseUrl + "../" + currentUser.getProfilePictureUrl();

                Glide.with(this)
                        .load(imageUrl)
                        .placeholder(R.drawable.ic_person)
                        .error(R.drawable.ic_person)
                        .into(profileImage);
            }

            // Populate form fields
            if (fullNameEditText != null) {
                fullNameEditText.setText(currentUser.getFullName());
            }
            if (phoneEditText != null) {
                phoneEditText.setText(currentUser.getPhone());
            }
            if (emailEditText != null) {
                emailEditText.setText(currentUser.getEmail());
            }
            if (addressEditText != null) {
                addressEditText.setText(currentUser.getAddress());
            }

            // Update user name display in header
            TextView userNameDisplay = rootView.findViewById(R.id.user_name_display);
            if (userNameDisplay != null) {
                userNameDisplay.setText(currentUser.getFullName());
            }

            // Update statistics (you can implement real data loading later)
            TextView totalOrdersCount = rootView.findViewById(R.id.total_orders_count);
            TextView totalSpentAmount = rootView.findViewById(R.id.total_spent_amount);
            TextView memberSinceDate = rootView.findViewById(R.id.member_since_date);

            if (totalOrdersCount != null) totalOrdersCount.setText("0"); // Replace with real data
            if (totalSpentAmount != null) totalSpentAmount.setText("৳0"); // Replace with real data
            if (memberSinceDate != null) {
                // You can format the user's creation date here
                memberSinceDate.setText("2024"); // Replace with real data
            }
        }
    }

    private void showImageSelectionDialog() {
        SweetAlertDialog dialog = new SweetAlertDialog(requireContext(), SweetAlertDialog.NORMAL_TYPE)
                .setTitleText(getString(R.string.select_image))
                .setContentText(getString(R.string.select_image_source))
                .setCancelText(getString(R.string.gallery))
                .setConfirmText(getString(R.string.camera))
                .showCancelButton(true)
                .setCancelClickListener(sweetAlertDialog -> {
                    sweetAlertDialog.dismissWithAnimation();
                    dispatchGalleryIntent();
                })
                .setConfirmClickListener(sweetAlertDialog -> {
                    sweetAlertDialog.dismissWithAnimation();
                    dispatchTakePictureIntent();
                });

        dialog.show();
    }

    private void dispatchTakePictureIntent() {
        // Check for camera permission
        if (ContextCompat.checkSelfPermission(requireContext(), Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            // Request camera permission
            cameraPermissionLauncher.launch(new String[]{Manifest.permission.CAMERA});
        } else {
            // Permission already granted, proceed with camera
            openCamera();
        }
    }

    private void openCamera() {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        if (takePictureIntent.resolveActivity(requireActivity().getPackageManager()) != null) {
            // Create the File where the photo should go
            try {
                photoFile = createImageFile();
            } catch (IOException ex) {
                // Error occurred while creating the File
                DialogUtils.showErrorDialog(requireContext(),
                    getString(R.string.error),
                    getString(R.string.error_creating_image_file));
                return;
            }

            // Continue only if the File was successfully created
            if (photoFile != null) {
                photoUri = FileProvider.getUriForFile(requireContext(),
                        "com.appystore.signupchoosedistrict.fileprovider",
                        photoFile);
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri);
                startActivityForResult(takePictureIntent, REQUEST_IMAGE_CAPTURE);
            }
        } else {
            DialogUtils.showErrorDialog(requireContext(),
                getString(R.string.error),
                getString(R.string.no_camera_app_found));
        }
    }

    private void dispatchGalleryIntent() {
        // Check for storage permission
        String permission = Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
                ? Manifest.permission.READ_MEDIA_IMAGES
                : Manifest.permission.READ_EXTERNAL_STORAGE;

        if (ContextCompat.checkSelfPermission(requireContext(), permission) != PackageManager.PERMISSION_GRANTED) {
            // Request storage permission
            storagePermissionLauncher.launch(new String[]{permission});
        } else {
            // Permission already granted, proceed with gallery
            openGallery();
        }
    }

    private void openGallery() {
        Intent galleryIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        startActivityForResult(galleryIntent, REQUEST_GALLERY_IMAGE);
    }

    private File createImageFile() throws IOException {
        // Create an image file name
        String timeStamp = String.valueOf(System.currentTimeMillis());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = requireActivity().getExternalFilesDir(null);
        return File.createTempFile(
                imageFileName,  /* prefix */
                ".jpg",         /* suffix */
                storageDir      /* directory */
        );
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == REQUEST_IMAGE_CAPTURE) {
                // Image captured from camera
                if (photoFile != null && photoFile.exists()) {
                    uploadProfilePicture(photoFile);

                    // Display the image
                    Glide.with(this)
                            .load(photoUri)
                            .placeholder(R.drawable.ic_person)
                            .error(R.drawable.ic_person)
                            .into(profileImage);
                } else {
                    DialogUtils.showErrorDialog(requireContext(),
                        getString(R.string.error),
                        getString(R.string.photo_file_not_found));
                }
            } else if (requestCode == REQUEST_GALLERY_IMAGE && data != null) {
                // Image selected from gallery
                Uri selectedImage = data.getData();

                try {
                    // Convert URI to File
                    photoFile = createImageFile();
                    requireActivity().getContentResolver().openInputStream(selectedImage).transferTo(java.nio.file.Files.newOutputStream(photoFile.toPath()));

                    // Upload the image
                    uploadProfilePicture(photoFile);

                    // Display the image
                    Glide.with(this)
                            .load(selectedImage)
                            .placeholder(R.drawable.ic_person)
                            .error(R.drawable.ic_person)
                            .into(profileImage);
                } catch (IOException e) {
                    DialogUtils.showErrorDialog(requireContext(),
                        getString(R.string.error),
                        getString(R.string.error_processing_image) + ": " + e.getMessage());
                }
            }
        }
    }

    private void uploadProfilePicture(File imageFile) {
        if (imageFile == null || !imageFile.exists()) {
            DialogUtils.showErrorDialog(requireContext(),
                getString(R.string.error),
                getString(R.string.invalid_image_file));
            return;
        }

        // Show loading dialog
        loadingDialog = DialogUtils.showLoadingDialog(requireContext(), getString(R.string.uploading_image));
        // Check if saveButton is not null before using it
        if (saveButton != null) {
            saveButton.setEnabled(false);
        }

        // Log session information before upload
        SessionManager sessionManager = new SessionManager(requireContext());
        Log.d("ProfileFragment", "Before upload - Session cookies: " + sessionManager.getCookies());

        // Get a fresh API service instance to ensure we have the latest cookies
        ApiService apiService = ApiClient.getApiService(requireContext());

        try {
            // Create request body for image
            RequestBody requestFile = RequestBody.create(MediaType.parse("image/*"), imageFile);
            MultipartBody.Part body = MultipartBody.Part.createFormData("profile_picture", imageFile.getName(), requestFile);

            // Create request body for user_id
            User user = sessionManager.getUser();
            if (user == null) {
                Toast.makeText(requireContext(), "Error: User not logged in", Toast.LENGTH_SHORT).show();
                if (saveButton != null) {
                    saveButton.setEnabled(true);
                    saveButton.setText("Save Changes");
                }
                return;
            }

            RequestBody userId = RequestBody.create(MediaType.parse("text/plain"), String.valueOf(user.getId()));
            Log.d("ProfileFragment", "Uploading profile picture for user ID: " + user.getId());

            // Make API call with user_id parameter
            apiService.uploadProfilePicture(body, userId).enqueue(new Callback<Object>() {
                @Override
                public void onResponse(Call<Object> call, Response<Object> response) {
                    // Dismiss loading dialog and reset button
                    if (loadingDialog != null) {
                        loadingDialog.dismissWithAnimation();
                    }
                    if (saveButton != null) {
                        saveButton.setEnabled(true);
                    }

                    if (response.isSuccessful() && response.body() != null) {
                        try {
                            // Parse response manually
                            Gson gson = new Gson();
                            String jsonString = gson.toJson(response.body());
                            JSONObject jsonResponse = new JSONObject(jsonString);

                            boolean success = jsonResponse.optBoolean("success", false);
                            String message = jsonResponse.optString("message", "");

                            if (success) {
                                // Get user data from response
                                JSONObject dataObject = jsonResponse.optJSONObject("data");
                                if (dataObject != null && dataObject.has("user")) {
                                    JSONObject userObject = dataObject.getJSONObject("user");

                                    // Create User object from JSON
                                    User updatedUser = gson.fromJson(userObject.toString(), User.class);
                                    sessionManager.saveUser(updatedUser);
                                    currentUser = updatedUser;

                                    // Show success message
                                    DialogUtils.showSuccessDialog(requireContext(),
                                        getString(R.string.success),
                                        getString(R.string.profile_picture_updated));
                                } else {
                                    // Show success message even if no user data
                                    DialogUtils.showSuccessDialog(requireContext(),
                                        getString(R.string.success),
                                        message);
                                }
                            } else {
                                // Show error message
                                DialogUtils.showErrorDialog(requireContext(),
                                    getString(R.string.error),
                                    message);
                            }
                        } catch (Exception e) {
                            Log.e("ProfileFragment", "Error parsing upload response", e);
                            DialogUtils.showErrorDialog(requireContext(),
                                getString(R.string.error),
                                getString(R.string.profile_picture_update_failed));
                        }
                    } else {
                        try {
                            // Try to get error message from response
                            String errorBody = response.errorBody() != null ? response.errorBody().string() : "Unknown error";

                            try {
                                JSONObject errorJson = new JSONObject(errorBody);
                                String errorMessage = errorJson.optString("message", getString(R.string.profile_picture_update_failed));
                                DialogUtils.showErrorDialog(requireContext(), getString(R.string.error), errorMessage);
                            } catch (JSONException jsonEx) {
                                DialogUtils.showErrorDialog(requireContext(),
                                    getString(R.string.error),
                                    getString(R.string.profile_picture_update_failed) + ": " + errorBody);
                            }
                        } catch (IOException e) {
                            DialogUtils.showErrorDialog(requireContext(),
                                getString(R.string.error),
                                getString(R.string.profile_picture_update_failed) + ": " + response.code());
                        }
                    }
                }

                @Override
                public void onFailure(Call<Object> call, Throwable t) {
                    // Dismiss loading dialog and reset button
                    if (loadingDialog != null) {
                        loadingDialog.dismissWithAnimation();
                    }
                    if (saveButton != null) {
                        saveButton.setEnabled(true);
                    }

                    // Show error message
                    String errorMessage = t.getMessage();
                    if (errorMessage != null && errorMessage.contains("Unable to resolve host")) {
                        errorMessage = getString(R.string.network_error);
                    }
                    DialogUtils.showErrorDialog(requireContext(), getString(R.string.error), errorMessage);
                }
            });
        } catch (Exception e) {
            // Dismiss loading dialog and reset button
            if (loadingDialog != null) {
                loadingDialog.dismissWithAnimation();
            }
            if (saveButton != null) {
                saveButton.setEnabled(true);
            }

            // Show error message
            DialogUtils.showErrorDialog(requireContext(),
                getString(R.string.error),
                getString(R.string.error_preparing_upload) + ": " + e.getMessage());
        }
    }

    private void saveProfileChanges() {
        // TODO: Implement save profile changes functionality
        DialogUtils.showInfoDialog(requireContext(),
            getString(R.string.coming_soon),
            getString(R.string.profile_update_coming_soon));
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // Dismiss any active loading dialog
        if (loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismissWithAnimation();
        }
    }
}
