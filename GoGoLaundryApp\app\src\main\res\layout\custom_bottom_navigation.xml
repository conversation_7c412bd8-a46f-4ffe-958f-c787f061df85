<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:layout_marginStart="20dp"
    android:layout_marginEnd="20dp"
    android:layout_marginBottom="20dp"
    android:clipToOutline="true"
    android:background="@drawable/bg_bottom_nav_glass"
    android:elevation="12dp">

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:itemIconSize="24dp"
        app:itemIconTint="@color/bottom_nav_icon_tint"
        app:itemTextColor="#9A67BB"
        app:labelVisibilityMode="labeled"
        app:itemRippleColor="@color/bottom_nav_ripple"
        app:itemBackground="@drawable/glass_toolbar_background"
        app:menu="@menu/bottom_nav_menu" />

</FrameLayout>
