<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background">

    <!-- App Bar with Collapsing Toolbar -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_scrollFlags="scroll|exitUntilCollapsed"
            app:contentScrim="#D7A1E5"
            app:expandedTitleMarginStart="16dp"
            app:expandedTitleMarginEnd="16dp"
            app:expandedTitleTextAppearance="@style/TextAppearance.AppCompat.Title"
            app:collapsedTitleTextAppearance="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/glass_hero_background"
                android:paddingStart="16dp"
                android:paddingTop="24dp"
                android:paddingEnd="16dp"
                android:paddingBottom="24dp"
                android:elevation="8dp"
                app:layout_collapseMode="parallax">

                <!-- Service Title with Glass Background -->
                <TextView
                    android:id="@+id/service_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:textStyle="bold"
                    android:fontFamily="@font/mahfuj"
                    android:background="@drawable/glass_section_background"
                    android:padding="16dp"
                    android:elevation="4dp"
                    android:gravity="center"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Dry Cleaning" />



            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_collapseMode="pin"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content with Glass Background -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="6dp"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="6dp"
        android:background="@drawable/glass_content_background"
        android:elevation="4dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="8dp">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <!-- Enhanced Responsive RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/items_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:clipChildren="false"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="120dp"
                    android:scrollbars="none"
                    android:overScrollMode="never"
                    android:nestedScrollingEnabled="true"
                    android:scrollbarStyle="outsideOverlay"
                    android:scrollbarFadeDuration="1000"
                    android:fadeScrollbars="true"
                    android:background="@android:color/transparent"
                    tools:listitem="@layout/item_laundry_item" />

                <!-- Loading State with Glass Effect -->
                <FrameLayout
                    android:id="@+id/progress_card"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/glass_dialog_background"
                    android:padding="24dp"
                    android:elevation="8dp"
                    android:visibility="gone">

                    <ProgressBar
                        android:id="@+id/progress_bar"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:layout_gravity="center"
                        android:indeterminateTint="@color/home_accent_blue" />
                </FrameLayout>

                <!-- Empty State with Glass Effect -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/empty_card"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="24dp"
                    android:background="@drawable/glass_empty_state_background"
                    android:padding="32dp"
                    android:elevation="6dp"
                    android:visibility="gone">

                    <!-- Empty State Icon with Glass Background -->
                    <FrameLayout
                        android:id="@+id/empty_icon_container"
                        android:layout_width="80dp"
                        android:layout_height="80dp"
                        android:background="@drawable/glass_icon_background"
                        android:elevation="4dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_empty_services"
                            android:alpha="0.8"
                            android:contentDescription="@string/no_items_found"
                            app:tint="@color/home_accent_blue" />
                    </FrameLayout>

                    <!-- Empty State Title -->
                    <TextView
                        android:id="@+id/empty_view"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="24dp"
                        android:fontFamily="@font/mahfuj"
                        android:text="@string/no_items_found"
                        android:textAlignment="center"
                        android:textAppearance="@style/TextAppearance.MaterialComponents.Headline6"
                        android:textColor="@android:color/white"
                        android:textStyle="bold"
                        android:background="@drawable/glass_section_background"
                        android:padding="12dp"
                        android:elevation="2dp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/empty_icon_container" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </FrameLayout>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </FrameLayout>



</androidx.coordinatorlayout.widget.CoordinatorLayout>
