<?php
/**
 * Order Details Page
 *
 * This page displays detailed information about a specific order
 */

// Include required files
require_once '../config/db.php';
require_once '../includes/functions.php';
require_once '../includes/OrderManager.php';

// Include authentication middleware
require_once 'auth.php';

// Check if order ID is provided
if (!isset($_GET['id'])) {
    $_SESSION['error_message'] = 'Order ID is required';
    header('Location: orders.php');
    exit;
}

// Get order ID
$orderId = (int)$_GET['id'];

// Initialize OrderManager
$orderManager = new OrderManager($pdo);

// Get order details
$order = $orderManager->getOrderById($orderId);

if (!$order) {
    $_SESSION['error_message'] = 'Order not found';
    header('Location: orders.php');
    exit;
}

// Get order items
$orderItems = $orderManager->getOrderItems($orderId);

// Get order status history
$statusHistory = $orderManager->getOrderStatusHistory($orderId);

// Get delivery personnel for assignment
$stmt = $pdo->prepare("SELECT id, full_name, phone FROM delivery_personnel WHERE is_active = 1 ORDER BY full_name");
$stmt->execute();
$deliveryPersonnel = $stmt->fetchAll();

// Handle order status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $newStatus = $_POST['new_status'];
    $notes = $_POST['notes'] ?? '';
    $deliveryPersonnelId = !empty($_POST['delivery_personnel_id']) ? (int)$_POST['delivery_personnel_id'] : null;

    $result = $orderManager->updateOrderStatus(
        $orderId,
        $newStatus,
        $notes,
        $adminData['id'],
        'admin',
        $deliveryPersonnelId
    );

    if ($result) {
        $_SESSION['success_message'] = 'Order status updated successfully';
    } else {
        $_SESSION['error_message'] = 'Failed to update order status';
    }

    // Redirect to refresh the page
    header("Location: order_details.php?id=$orderId");
    exit;
}

// Page title and breadcrumbs
$pageTitle = 'Order Details';
$breadcrumbs = [
    ['text' => 'Dashboard', 'link' => 'index.php'],
    ['text' => 'Orders', 'link' => 'orders.php'],
    ['text' => 'Order #' . $order['order_number'], 'link' => '']
];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 text-gray-800">Order #<?= htmlspecialchars($order['order_number']) ?></h1>
        <div>
            <a href="orders.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Orders
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                <i class="fas fa-edit"></i> Update Status
            </button>
            <button type="button" class="btn btn-info" onclick="printOrder()">
                <i class="fas fa-print"></i> Print
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Order Information -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Information</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">Order Number</th>
                                <td><?= htmlspecialchars($order['order_number']) ?></td>
                            </tr>
                            <tr>
                                <th>Tracking Number</th>
                                <td><?= htmlspecialchars($order['tracking_number']) ?></td>
                            </tr>
                            <tr>
                                <th>Date</th>
                                <td><?= date('M d, Y H:i', strtotime($order['created_at'])) ?></td>
                            </tr>
                            <tr>
                                <th>Status</th>
                                <td>
                                    <span class="badge bg-<?= getStatusBadgeClass($order['status']) ?>">
                                        <?= formatStatus($order['status']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Payment Method</th>
                                <td><?= formatPaymentMethod($order['payment_method']) ?></td>
                            </tr>
                            <tr>
                                <th>Payment Status</th>
                                <td>
                                    <span class="badge bg-<?= getPaymentStatusBadgeClass($order['payment_status']) ?>">
                                        <?= formatPaymentStatus($order['payment_status']) ?>
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Notes</th>
                                <td><?= htmlspecialchars($order['notes'] ?? 'N/A') ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customer Information</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th width="30%">Name</th>
                                <td><?= htmlspecialchars($order['customer_name']) ?></td>
                            </tr>
                            <tr>
                                <th>Phone</th>
                                <td><?= htmlspecialchars($order['customer_phone']) ?></td>
                            </tr>
                            <tr>
                                <th>Email</th>
                                <td><?= htmlspecialchars($order['customer_email'] ?? 'N/A') ?></td>
                            </tr>
                            <tr>
                                <th>Pickup Address</th>
                                <td><?= htmlspecialchars($order['pickup_address']) ?></td>
                            </tr>
                            <tr>
                                <th>Pickup Date</th>
                                <td><?= date('M d, Y', strtotime($order['pickup_date'])) ?></td>
                            </tr>
                            <tr>
                                <th>Pickup Time Slot</th>
                                <td><?= htmlspecialchars($order['pickup_time_slot']) ?></td>
                            </tr>
                            <tr>
                                <th>Delivery Address</th>
                                <td><?= htmlspecialchars($order['delivery_address']) ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Order Items -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>Service</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($orderItems)): ?>
                                    <tr>
                                        <td colspan="5" class="text-center">No items found</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($orderItems as $item): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($item['name']) ?></td>
                                            <td><?= htmlspecialchars($item['service_name']) ?></td>
                                            <td><?= number_format($item['price'], 2) ?> BDT</td>
                                            <td><?= $item['quantity'] ?></td>
                                            <td><?= number_format($item['subtotal'], 2) ?> BDT</td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-right">Subtotal:</th>
                                    <td><?= number_format($order['subtotal'], 2) ?> BDT</td>
                                </tr>
                                <?php if ($order['discount'] > 0): ?>
                                    <tr>
                                        <th colspan="4" class="text-right">Discount:</th>
                                        <td>-<?= number_format($order['discount'], 2) ?> BDT</td>
                                    </tr>
                                <?php endif; ?>
                                <tr>
                                    <th colspan="4" class="text-right">Delivery Fee:</th>
                                    <td><?= number_format($order['delivery_fee'], 2) ?> BDT</td>
                                </tr>
                                <tr>
                                    <th colspan="4" class="text-right">Total:</th>
                                    <td><?= number_format($order['total'], 2) ?> BDT</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Personnel -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Delivery Personnel</h6>
                </div>
                <div class="card-body">
                    <?php if ($order['delivery_personnel_id']): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">Name</th>
                                    <td><?= htmlspecialchars($order['delivery_person_name']) ?></td>
                                </tr>
                                <tr>
                                    <th>Phone</th>
                                    <td><?= htmlspecialchars($order['delivery_person_phone']) ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                                <i class="fas fa-exchange-alt"></i> Change Delivery Personnel
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle me-2"></i> No delivery personnel assigned yet.
                        </div>
                        <div class="mt-3">
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                                <i class="fas fa-user-plus"></i> Assign Delivery Personnel
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Status History -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Status History</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Status</th>
                            <th>Date & Time</th>
                            <th>Updated By</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($statusHistory)): ?>
                            <tr>
                                <td colspan="4" class="text-center">No status history found</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($statusHistory as $history): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-<?= getStatusBadgeClass($history['status']) ?>">
                                            <?= formatStatus($history['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= date('M d, Y H:i', strtotime($history['created_at'])) ?></td>
                                    <td><?= htmlspecialchars($history['updated_by_name'] ?? 'System') ?></td>
                                    <td><?= htmlspecialchars($history['notes'] ?? 'N/A') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateStatusModalLabel">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="order_details.php?id=<?= $orderId ?>">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="new_status" class="form-label">New Status</label>
                        <select class="form-control" id="new_status" name="new_status" required>
                            <option value="placed" <?= $order['status'] === 'placed' ? 'selected' : '' ?>>Placed</option>
                            <option value="confirmed" <?= $order['status'] === 'confirmed' ? 'selected' : '' ?>>Confirmed</option>
                            <option value="pickup_scheduled" <?= $order['status'] === 'pickup_scheduled' ? 'selected' : '' ?>>Pickup Scheduled</option>
                            <option value="picked_up" <?= $order['status'] === 'picked_up' ? 'selected' : '' ?>>Picked Up</option>
                            <option value="processing" <?= $order['status'] === 'processing' ? 'selected' : '' ?>>Processing</option>
                            <option value="ready_for_delivery" <?= $order['status'] === 'ready_for_delivery' ? 'selected' : '' ?>>Ready for Delivery</option>
                            <option value="out_for_delivery" <?= $order['status'] === 'out_for_delivery' ? 'selected' : '' ?>>Out for Delivery</option>
                            <option value="delivered" <?= $order['status'] === 'delivered' ? 'selected' : '' ?>>Delivered</option>
                            <option value="cancelled" <?= $order['status'] === 'cancelled' ? 'selected' : '' ?>>Cancelled</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="delivery_personnel_id" class="form-label">Assign Delivery Personnel</label>
                        <select class="form-control" id="delivery_personnel_id" name="delivery_personnel_id">
                            <option value="">Select Delivery Personnel</option>
                            <?php foreach ($deliveryPersonnel as $dp): ?>
                                <option value="<?= $dp['id'] ?>" <?= $order['delivery_personnel_id'] == $dp['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($dp['full_name']) ?> (<?= htmlspecialchars($dp['phone']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="update_status" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
    function printOrder() {
        window.print();
    }
</script>

<?php
/**
 * Get status badge class
 *
 * @param string $status Order status
 * @return string Badge class
 */
function getStatusBadgeClass($status) {
    switch ($status) {
        case 'placed': return 'secondary';
        case 'confirmed': return 'info';
        case 'pickup_scheduled': return 'primary';
        case 'picked_up': return 'warning';
        case 'processing': return 'warning';
        case 'ready_for_delivery': return 'info';
        case 'out_for_delivery': return 'primary';
        case 'delivered': return 'success';
        case 'cancelled': return 'danger';
        default: return 'secondary';
    }
}

/**
 * Format status
 *
 * @param string $status Order status
 * @return string Formatted status
 */
function formatStatus($status) {
    return ucwords(str_replace('_', ' ', $status));
}

/**
 * Get payment status badge class
 *
 * @param string $status Payment status
 * @return string Badge class
 */
function getPaymentStatusBadgeClass($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'paid': return 'success';
        case 'failed': return 'danger';
        case 'refunded': return 'info';
        default: return 'secondary';
    }
}

/**
 * Format payment status
 *
 * @param string $status Payment status
 * @return string Formatted status
 */
function formatPaymentStatus($status) {
    return ucfirst($status);
}

/**
 * Format payment method
 *
 * @param string $method Payment method
 * @return string Formatted method
 */
function formatPaymentMethod($method) {
    switch ($method) {
        case 'cash': return 'Cash on Delivery';
        case 'card': return 'Card Payment';
        case 'mobile_banking': return 'Mobile Banking';
        default: return ucfirst($method);
    }
}
?>
