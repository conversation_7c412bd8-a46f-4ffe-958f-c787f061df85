<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="mobile_payment_providers">
        <item>bKash</item>
        <item>Nagad</item>
        <item>Rocket</item>
    </string-array>
    <string-array name="payment_methods">
        <item>Cash on Delivery</item>
        <item>bKash</item>
        <item>Nagad</item>
        <item>Rocket</item>
        <item>Credit/Debit Card</item>
    </string-array>
    <color name="accent">#FF4081</color>
    <color name="accent_dark">#E91E63</color>
    <color name="background">#FFFFFF</color>
    <color name="background_dark">#EEEEEE</color>
    <color name="background_dark_new">#0F0F23</color>
    <color name="background_light">#F5F5F5</color>
    <color name="black">#000000</color>
    <color name="black_50">#80000000</color>
    <color name="blue_light">#E3F2FD</color>
    <color name="bottom_nav_icon_tint">#FF6200EE</color>
    <color name="bottom_nav_ripple">#336200EE</color>
    <color name="bottom_nav_text_color">#FF6200EE</color>
    <color name="card_background">#FFFFFF</color>
    <color name="card_background_dark">#1A1A2E</color>
    <color name="card_border">#DDDDDD</color>
    <color name="colorAccent">#BB342A</color>
    <color name="colorPrimary">#BA372E</color>
    <color name="colorPrimary_30">#4DBA372E</color>
    <color name="colorSecondary">#FF9800</color>
    <color name="divider">#EEEEEE</color>
    <color name="divider_dark">#424242</color>
    <color name="divider_light">#E0E0E0</color>
    <color name="error">#F44336</color>
    <color name="error_color">#F44336</color>
    <color name="glass_background">#F0FFFFFF</color>
    <color name="glass_border">#30FFFFFF</color>
    <color name="glass_input_background">#10FFFFFF</color>
    <color name="glass_shadow">#0D000000</color>
    <color name="gradient_blue_end">#1976D2</color>
    <color name="gradient_blue_purple">#667eea</color>
    <color name="gradient_blue_start">#2196F3</color>
    <color name="gradient_mint_end">#fed6e3</color>
    <color name="gradient_mint_start">#a8edea</color>
    <color name="gradient_ocean_end">#764ba2</color>
    <color name="gradient_ocean_start">#667eea</color>
    <color name="gradient_purple_end">#764ba2</color>
    <color name="gradient_purple_start">#667eea</color>
    <color name="gradient_sunset_end">#fecfef</color>
    <color name="gradient_sunset_start">#ff9a9e</color>
    <color name="gray_light">#F8756C</color>
    <color name="home_accent_blue">#4FC3F7</color>
    <color name="home_accent_green">#4CAF50</color>
    <color name="home_accent_orange">#FF9800</color>
    <color name="home_accent_purple">#9C27B0</color>
    <color name="home_card_background">#FFFFFF</color>
    <color name="home_card_shadow">#1A000000</color>
    <color name="home_divider_subtle">#F0F0F0</color>
    <color name="home_glass_background">#E6FFFFFF</color>
    <color name="home_glass_border">#4DFFFFFF</color>
    <color name="home_hero_gradient_end">#764ba2</color>
    <color name="home_hero_gradient_start">#667eea</color>
    <color name="home_section_background">#FAFAFA</color>
    <color name="home_text_on_gradient">#FFFFFF</color>
    <color name="info">#2196F3</color>
    <color name="on_surface_variant">#49454F</color>
    <color name="picker_background_overlay">#F8FFFE</color>
    <color name="picker_border_color">#E1F5FE</color>
    <color name="picker_card_background">#FFFFFF</color>
    <color name="picker_card_shadow">#1A000000</color>
    <color name="picker_gradient_end">#1976D2</color>
    <color name="picker_gradient_start">#4FC3F7</color>
    <color name="picker_ripple_color">#1A1976D2</color>
    <color name="picker_selected_background">#E3F2FD</color>
    <color name="picker_selected_text">#1976D2</color>
    <color name="picker_unselected_text">#757575</color>
    <color name="primary">#BA372E</color>
    <color name="primary_dark">#1565C0</color>
    <color name="primary_light">#BBDEFB</color>
    <color name="primary_light_transparent">#331976D2</color>
    <color name="primary_modern">#BA372E</color>
    <color name="rating_background">#16213E</color>
    <color name="rating_star">#FFD700</color>
    <color name="search_accent_blue">#4FC3F7</color>
    <color name="search_accent_purple">#9C27B0</color>
    <color name="search_background_primary">#66FFFFFF</color>
    <color name="search_background_secondary">#4DFFFFFF</color>
    <color name="search_border_primary">#80FFFFFF</color>
    <color name="search_border_secondary">#99FFFFFF</color>
    <color name="search_highlight">#CCFFFFFF</color>
    <color name="search_hint_color">#B3FFFFFF</color>
    <color name="search_icon_primary">#FFFFFF</color>
    <color name="search_icon_secondary">#E0FFFFFF</color>
    <color name="search_ripple_effect">#33FFFFFF</color>
    <color name="search_shadow">#1A000000</color>
    <color name="search_text_primary">#FFFFFF</color>
    <color name="search_text_secondary">#E0FFFFFF</color>
    <color name="shimmer_highlight">#FFFFFF</color>
    <color name="shimmer_placeholder">#F0F0F0</color>
    <color name="status_busy">#FF9800</color>
    <color name="status_closed">#F44336</color>
    <color name="status_open">#4CAF50</color>
    <color name="stroke_color">#E0E0E0</color>
    <color name="success">#4CAF50</color>
    <color name="surface_elevated">#FFFFFF</color>
    <color name="surface_elevated_tint">#F8F9FA</color>
    <color name="text_hint_modern">#9E9E9E</color>
    <color name="text_primary">#212121</color>
    <color name="text_primary_dark">#FFFFFF</color>
    <color name="text_secondary">#757575</color>
    <color name="text_secondary_dark">#B0B0B0</color>
    <color name="transparent">#00000000</color>
    <color name="warning">#FFC107</color>
    <color name="white">#FFFFFF</color>
    <color name="white_10">#1AFFFFFF</color>
    <color name="white_20">#33FFFFFF</color>
    <color name="white_30">#4DFFFFFF</color>
    <color name="white_50">#80FFFFFF</color>
    <color name="white_70">#B3FFFFFF</color>
    <dimen name="avatar_size">40dp</dimen>
    <dimen name="bottom_sheet_handle_height">4dp</dimen>
    <dimen name="bottom_sheet_handle_width">40dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="button_height">56dp</dimen>
    <dimen name="card_corner_radius">8dp</dimen>
    <dimen name="card_corner_radius_small">4dp</dimen>
    <dimen name="compact_service_card_padding">12dp</dimen>
    <dimen name="compact_service_image_height">120dp</dimen>
    <dimen name="compact_service_image_margin">16dp</dimen>
    <dimen name="date_picker_header_height">72dp</dimen>
    <dimen name="divider_height">1dp</dimen>
    <dimen name="elevation_appbar">4dp</dimen>
    <dimen name="elevation_card">2dp</dimen>
    <dimen name="elevation_card_small">1dp</dimen>
    <dimen name="elevation_dialog">24dp</dimen>
    <dimen name="elevation_fab">6dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="grid_spacing">8dp</dimen>
    <dimen name="icon_size_large">48dp</dimen>
    <dimen name="icon_size_medium">36dp</dimen>
    <dimen name="icon_size_small">24dp</dimen>
    <dimen name="input_field_height">56dp</dimen>
    <dimen name="map_search_margin">16dp</dimen>
    <dimen name="margin_extra_large">32dp</dimen>
    <dimen name="margin_extra_small">4dp</dimen>
    <dimen name="margin_huge">48dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="service_card_corner_radius">16dp</dimen>
    <dimen name="service_card_elevation">4dp</dimen>
    <dimen name="service_card_max_height">220dp</dimen>
    <dimen name="service_card_min_height">180dp</dimen>
    <dimen name="service_icon_container_size">80dp</dimen>
    <dimen name="service_icon_size">48dp</dimen>
    <dimen name="shop_card_margin">8dp</dimen>
    <dimen name="shop_image_size">60dp</dimen>
    <dimen name="shop_profile_image_size">80dp</dimen>
    <dimen name="star_margin">2dp</dimen>
    <dimen name="star_size">16dp</dimen>
    <dimen name="text_size_extra_large">20sp</dimen>
    <dimen name="text_size_headline">26sp</dimen>
    <dimen name="text_size_huge">24sp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_micro">12sp</dimen>
    <dimen name="text_size_small">14sp</dimen>
    <dimen name="text_size_title">22sp</dimen>
    <dimen name="time_picker_height">256dp</dimen>
    <dimen name="toolbar_height">56dp</dimen>
    <integer name="orders_grid_columns">2</integer>
    <string name="about_app">Learn more about the app</string>
    <string name="about_app_description">GoGoLaundry is a laundry service app that helps you get your laundry done with ease.</string>
    <string name="about_app_title">About GoGoLaundry</string>
    <string name="add">Add</string>
    <string name="add_to_cart">ADD TO CART</string>
    <string name="add_to_cart_bangla">কার্টে যোগ করুন</string>
    <string name="add_to_cart_short">Add</string>
    <string name="add_to_cart_short_bangla">যোগ করুন</string>
    <string name="address">Village/Address</string>
    <string name="all">All</string>
    <string name="already_have_account">Already have an account? Login</string>
    <string name="any_rating">Any rating</string>
    <string name="app_name">Go Go Laundry</string>
    <string name="apply">Apply</string>
    <string name="away_format">%.1f km away</string>
    <string name="back">Back</string>
    <string name="business_hours_warning">Please select a time between 9 AM and 6 PM (business hours)</string>
    <string name="busy">Busy</string>
    <string name="call">Call</string>
    <string name="camera">Camera</string>
    <string name="camera_permission_required">Camera permission is required to take photos</string>
    <string name="cancel">Cancel</string>
    <string name="cancel_order">Cancel Order</string>
    <string name="cart">Cart</string>
    <string name="cart_is_empty">Your cart is empty</string>
    <string name="change_password">Change Password</string>
    <string name="change_password_coming_soon">Change password functionality will be implemented soon</string>
    <string name="checkout">Checkout</string>
    <string name="choose_image_source">ছবি তোলার জন্য ক্যামেরা বা গ্যালারি থেকে নির্বাচন করুন</string>
    <string name="clear_cart">Clear Cart</string>
    <string name="close">Close</string>
    <string name="closed">Closed</string>
    <string name="closed_today">Closed Today</string>
    <string name="coming_soon">Coming Soon</string>
    <string name="commission_rate">Commission Rate</string>
    <string name="confirm_new_password">Confirm New Password</string>
    <string name="confirm_password">Confirm Password</string>
    <string name="contact_for_ads">Contact for Advertisements</string>
    <string name="contact_phone">Please call us at: +880 1234567890</string>
    <string name="contact_us">Contact Us</string>
    <string name="continue_text">Continue</string>
    <string name="current_location">Current Location</string>
    <string name="custom_price">Custom Price</string>
    <string name="dashboard">Dashboard</string>
    <string name="date">Date</string>
    <string name="decrease_quantity">Decrease Quantity</string>
    <string name="default_price">Default Price</string>
    <string name="delivery_address">Delivery Address</string>
    <string name="delivery_date_must_be_after_pickup">Delivery date must be after pickup date</string>
    <string name="delivery_details">Delivery Details</string>
    <string name="delivery_fee">Delivery Fee</string>
    <string name="delivery_fee_format">Delivery Fee: ৳%.2f</string>
    <string name="delivery_time_must_be_after_pickup">Delivery time must be at least 1 hour after pickup time</string>
    <string name="directions">Directions</string>
    <string name="discount">Discount</string>
    <string name="distance_km">%.1f km</string>
    <string name="district">District</string>
    <string name="division">Division</string>
    <string name="dont_have_account">Don\'t have an account? Sign up</string>
    <string name="download">Download</string>
    <string name="email">Email (Optional)</string>
    <string name="enter_new_password">Enter your new password</string>
    <string name="enter_otp">Enter OTP</string>
    <string name="enter_phone_for_reset">Enter your registered phone number to reset your password</string>
    <string name="error">Error</string>
    <string name="error_creating_image_file">Error creating image file</string>
    <string name="error_loading_items">Error loading items</string>
    <string name="error_loading_services">Error loading services</string>
    <string name="error_loading_shop_details">Error loading shop details</string>
    <string name="error_loading_shops">Error loading shops</string>
    <string name="error_marking_all_read">Error marking all notifications as read</string>
    <string name="error_marking_read">Error marking notification as read</string>
    <string name="error_notification_not_found">Notification not found</string>
    <string name="error_preparing_upload">Error preparing upload</string>
    <string name="error_processing_image">Error processing image</string>
    <string name="estimated_delivery">Estimated Delivery: %d hours</string>
    <string name="estimated_time">Estimated Time</string>
    <string name="estimated_time_hours">%d hours</string>
    <string name="facebook_error">Could not open Facebook page</string>
    <string name="facebook_group">Facebook Group</string>
    <string name="facebook_group_error">Could not open Facebook group</string>
    <string name="facebook_page">Facebook Page</string>
    <string name="filter">Filter</string>
    <string name="forgot_password">Forgot Password?</string>
    <string name="forgot_password_title">Forgot Password</string>
    <string name="friday">Friday</string>
    <string name="fulfillment_type">Fulfillment Type</string>
    <string name="full_name">Full Name</string>
    <string name="full_screen_image">Full Screen Image</string>
    <string name="gallery">Gallery</string>
    <string name="gcm_defaultSenderId" translatable="false">523301621504</string>
    <string name="google_api_key" translatable="false">AIzaSyASl3UlvsWsfylrHNHLWUOxc2Lcln6PI0g</string>
    <string name="google_app_id" translatable="false">1:523301621504:android:59b7b8816b0e2b7604ede5</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyASl3UlvsWsfylrHNHLWUOxc2Lcln6PI0g</string>
    <string name="google_storage_bucket" translatable="false">gogolaundry-c4dd1.firebasestorage.app</string>
    <string name="hello_user">Hello, %1$s!</string>
    <string name="home">Home</string>
    <string name="hours_format">%d hours</string>
    <string name="image_save_failed">Failed to save image</string>
    <string name="image_saved">Image saved to gallery</string>
    <string name="image_share_failed">Failed to share image</string>
    <string name="image_shared">Image shared successfully</string>
    <string name="increase_quantity">Increase Quantity</string>
    <string name="invalid_credentials">Invalid phone number or password.</string>
    <string name="invalid_email">Please enter a valid email address</string>
    <string name="invalid_image_file">Error: Invalid image file</string>
    <string name="invalid_name">Please enter a valid name (at least 2 characters)</string>
    <string name="invalid_otp">Please enter a valid OTP</string>
    <string name="invalid_password">Password must be at least %d characters</string>
    <string name="invalid_phone">Please enter a valid phone number</string>
    <string name="item_image">Item Image</string>
    <string name="items">Items</string>
    <string name="items_subtitle">Select items for professional laundry service</string>
    <string name="language">Language</string>
    <string name="language_already_selected">This language is already selected</string>
    <string name="language_changed">Language changed</string>
    <string name="language_description">Choose your preferred language</string>
    <string name="loading">Loading…</string>
    <string name="location_not_available">Location not available</string>
    <string name="location_permission_denied">Location permission denied</string>
    <string name="logging_in">Logging in…</string>
    <string name="login">Login</string>
    <string name="login_failed">Login failed. Please try again.</string>
    <string name="login_title">Go Go Laundry</string>
    <string name="login_with_otp">Login with OTP</string>
    <string name="login_with_password">Login with Password</string>
    <string name="logout">Logout</string>
    <string name="logout_button">Logout</string>
    <string name="manual_dropoff">Manual Drop-off</string>
    <string name="manual_dropoff_desc">You will bring items to the shop and collect them</string>
    <string name="mark_all_as_read">Mark all as read</string>
    <string name="mark_as_read">Mark as read</string>
    <string name="min_order_amount">Minimum Order: ৳%.2f</string>
    <string name="monday">Monday</string>
    <string name="my_orders">My Orders</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="nearby_shops">Nearby Shops</string>
    <string name="nearest_zone">Nearest Zone</string>
    <string name="network_error">Network error. Please check your internet connection.</string>
    <string name="network_error_shops">Network error. Please check your connection.</string>
    <string name="new_password">New Password</string>
    <string name="next">Next</string>
    <string name="no_camera_app_found">No camera app found</string>
    <string name="no_items_found">No items found</string>
    <string name="no_items_found_shop">No items found for this shop</string>
    <string name="no_notifications">No notifications</string>
    <string name="no_notifications_message">You don\'t have any notifications yet.</string>
    <string name="no_orders_found">No orders found</string>
    <string name="no_orders_found_message">You don\'t have any orders yet. Place your first order to get started!</string>
    <string name="no_services_available">No services available</string>
    <string name="no_services_found">No services found</string>
    <string name="no_shops_found">No shops found</string>
    <string name="no_shops_nearby">No shops found nearby</string>
    <string name="not_provided">Not provided</string>
    <string name="notes">Notes (Optional)</string>
    <string name="notification_icon">Notification Icon</string>
    <string name="notification_image">Notification Image</string>
    <string name="notification_order_status">Order Status Update</string>
    <string name="notification_promo">Promotion</string>
    <string name="notification_system">System</string>
    <string name="notifications">Notifications</string>
    <string name="ok">OK</string>
    <string name="open">Open</string>
    <string name="operating_hours">Operating Hours</string>
    <string name="order_cancelled">Cancelled</string>
    <string name="order_confirmed">Order Confirmed</string>
    <string name="order_date">Order Date: %1$s</string>
    <string name="order_delivered">Delivered</string>
    <string name="order_details">Order Details</string>
    <string name="order_id">Order #%1$s</string>
    <string name="order_items">Order Items</string>
    <string name="order_now">Order Now</string>
    <string name="order_picked_up">Picked Up</string>
    <string name="order_placed">Order Placed</string>
    <string name="order_placement_coming_soon">Order placement coming soon</string>
    <string name="order_processing">Processing</string>
    <string name="order_progress">Order Progress</string>
    <string name="order_ready">Ready for Delivery</string>
    <string name="order_status">Status: %1$s</string>
    <string name="order_summary">Order Summary</string>
    <string name="order_total">Total: ৳%1$s</string>
    <string name="order_tracking">Order Tracking</string>
    <string name="orders">Orders</string>
    <string name="orders_subtitle">Track and manage your laundry orders</string>
    <string name="original_price">৳%.2f</string>
    <string name="otp">OTP</string>
    <string name="otp_expired">OTP expired. Please request a new one.</string>
    <string name="otp_required">OTP Required</string>
    <string name="otp_send_failed">Failed to send OTP. Please try again.</string>
    <string name="otp_sent">OTP sent successfully</string>
    <string formatted="false" name="otp_timer">Time remaining: %d:%02d</string>
    <string name="otp_verification">OTP Verification</string>
    <string name="otp_verification_failed">OTP verification failed. Please try again.</string>
    <string name="otp_verified">OTP verified successfully</string>
    <string name="out_of_stock">OUT OF STOCK</string>
    <string name="password">Password</string>
    <string name="password_required">Password is required</string>
    <string name="password_reset_failed">Failed to reset password. Please try again.</string>
    <string name="password_reset_success">Password reset successfully</string>
    <string name="passwords_dont_match">Passwords don\'t match</string>
    <string name="payment_method">Payment Method: %1$s</string>
    <string name="payment_status">Payment Status: %1$s</string>
    <string name="permission_required">Permission Required</string>
    <string name="phone_already_registered">Phone number already registered. Please login instead.</string>
    <string name="phone_not_available">Phone number not available</string>
    <string name="phone_not_registered">Phone number not registered. Please sign up first.</string>
    <string name="phone_number">Phone Number</string>
    <string name="photo_file_not_found">Error: Photo file not found</string>
    <string name="pickup_delivery">Pickup &amp; Delivery</string>
    <string name="pickup_delivery_desc">We will pick up from your location and deliver back</string>
    <string name="pickup_details">Pickup Details</string>
    <string name="place_order">Place Order</string>
    <string name="platform_earnings">Platform Earnings</string>
    <string name="please_wait">Please wait…</string>
    <string name="popular">Popular</string>
    <string name="price">Price</string>
    <string name="price_format">৳%1$s/item</string>
    <string name="price_per_kg">৳%1$d/kg</string>
    <string name="price_per_piece">৳%1$d/piece</string>
    <string name="price_tk">৳%.2f</string>
    <string name="profile">Profile</string>
    <string name="profile_picture_update_failed">Failed to update profile picture</string>
    <string name="profile_picture_updated">Profile picture updated successfully</string>
    <string name="profile_update_coming_soon">Profile update functionality will be implemented soon</string>
    <string name="project_id" translatable="false">gogolaundry-c4dd1</string>
    <string name="quantity">Quantity</string>
    <string name="quantity_format">Qty: %1$s %2$s</string>
    <string name="rating_and_above">%.1f+ stars</string>
    <string name="rating_format">%.1f</string>
    <string name="read_notifications_hidden">Read notifications are automatically hidden</string>
    <string name="refresh">Refresh</string>
    <string name="registration_failed">Registration failed. Please try again.</string>
    <string name="remove">Remove</string>
    <string name="remove_item">Remove Item</string>
    <string name="reorder">Reorder</string>
    <string name="required_field">This field is required</string>
    <string name="resend_otp">Resend OTP</string>
    <string name="reset_password">Reset Password</string>
    <string name="resetting">Resetting…</string>
    <string name="resetting_password">Resetting password…</string>
    <string name="review_format">(%d review)</string>
    <string name="reviews_format">(%d reviews)</string>
    <string name="saturday">Saturday</string>
    <string name="save">Save</string>
    <string name="search">Search</string>
    <string name="search_shops">🔍 খুঁজুন দোকান, এলাকা বা সেবা…</string>
    <string name="search_shops_english">🔍 Search shops, areas or services…</string>
    <string name="select_date">Select Date</string>
    <string name="select_delivery_date">Select Delivery Date</string>
    <string name="select_delivery_time">Select Delivery Time</string>
    <string name="select_district">Select District</string>
    <string name="select_division">Select Division</string>
    <string name="select_fulfillment_type">Select how you want to handle your order</string>
    <string name="select_image">Select Image</string>
    <string name="select_image_source">Choose image source</string>
    <string name="select_pickup_date">Select Pickup Date</string>
    <string name="select_pickup_time">Select Pickup Time</string>
    <string name="select_time">Select Time</string>
    <string name="select_upazilla">Select Upazilla</string>
    <string name="send_otp">Send OTP</string>
    <string name="sending">Sending…</string>
    <string name="sending_otp">Sending OTP…</string>
    <string name="server_error">Server error. Please try again later.</string>
    <string name="service_description">Service Description</string>
    <string name="service_details">Service Details</string>
    <string name="service_icon">Service Icon</string>
    <string name="service_name">Service Name</string>
    <string name="services">Services</string>
    <string name="services_colon">Services:</string>
    <string name="services_subtitle">Choose from our wide range of laundry services</string>
    <string name="settings">Settings</string>
    <string name="share">Share</string>
    <string name="shop_details">Shop Details</string>
    <string name="shop_earnings">Shop Earnings</string>
    <string name="shop_list">Shop List</string>
    <string name="shop_map">Shop Map</string>
    <string name="shop_not_found">Shop not found</string>
    <string name="sign_up">Sign Up</string>
    <string name="signing_up">Signing up…</string>
    <string name="status_cancelled">Cancelled</string>
    <string name="status_confirmed">Confirmed</string>
    <string name="status_delivered">Delivered</string>
    <string name="status_history">Status History</string>
    <string name="status_out_for_delivery">Out for Delivery</string>
    <string name="status_picked_up">Picked Up</string>
    <string name="status_pickup_scheduled">Pickup Scheduled</string>
    <string name="status_placed">Placed</string>
    <string name="status_processing">Processing</string>
    <string name="status_ready">Ready</string>
    <string name="stay_logged_in">Stay logged in</string>
    <string name="step_1">Step 1: Basic Information</string>
    <string name="step_2">Step 2: OTP Verification</string>
    <string name="step_3">Step 3: Password &amp; Location</string>
    <string name="storage_permission_required">Storage permission is required to select photos</string>
    <string name="submit">Submit</string>
    <string name="subtotal">Subtotal</string>
    <string name="subtotal_format">৳%1$s</string>
    <string name="success">Success</string>
    <string name="sunday">Sunday</string>
    <string name="thursday">Thursday</string>
    <string name="time">Time</string>
    <string name="time_format">%s - %s</string>
    <string name="total">Total</string>
    <string name="track_order">Track Order</string>
    <string name="try_again">Try Again</string>
    <string name="tuesday">Tuesday</string>
    <string name="unable_to_get_location">Unable to get current location</string>
    <string name="unknown_error">An unknown error occurred. Please try again.</string>
    <string name="upazilla">Upazilla</string>
    <string name="update_profile">Update Profile</string>
    <string name="uploading_image">Uploading image…</string>
    <string name="verify">Verify</string>
    <string name="verify_and_login">Verify &amp; Login</string>
    <string name="verifying">Verifying…</string>
    <string name="verifying_otp">Verifying OTP…</string>
    <string name="version">Version %1$s</string>
    <string name="view_all">View All</string>
    <string name="view_cart">VIEW CART</string>
    <string name="view_cart_count">VIEW CART (%1$d)</string>
    <string name="view_details">View Details</string>
    <string name="wednesday">Wednesday</string>
    <string name="welcome">Welcome</string>
    <string name="welcome_user">Welcome, %1$s</string>
    <string name="within_km">Within %d km</string>
    <string name="your_information">Your Information</string>
    <style name="AddToCartButtonStyle" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="cornerRadius">20dp</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="elevation">2dp</item>
    </style>
    <style name="AlertDialogButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>
    <style name="AlertDialogCorners">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>
    <style name="AlertDialogTheme" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="shapeAppearanceOverlay">@style/AlertDialogCorners</item>
    </style>
    <style name="Base.Theme.GoGoLaundry" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_color</item>
        
        <item name="android:colorBackground">@color/background</item>
    </style>
    <style name="CircleBackground">
        <item name="android:background">@drawable/circle_background</item>
    </style>
    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="CircularImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="FilterChipStyle" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="android:textColor">@color/primary</item>
        <item name="chipBackgroundColor">@color/chip_background_selector</item>
        <item name="android:textSize">14sp</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipMinTouchTargetSize">32dp</item>
        <item name="chipStrokeWidth">0dp</item>
    </style>
    <style name="ModernFilterButtonStyle" parent="">
        <item name="android:background">@drawable/modern_filter_button_background</item>
        <item name="android:tint">@color/black</item>
        <item name="android:scaleType">center</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="ModernSearchBarStyle" parent="">
        <item name="android:background">@drawable/modern_search_bar_selector</item>
        <item name="android:elevation">12dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>
    <style name="ModernSearchIconStyle" parent="">
        <item name="android:tint">@color/search_icon_primary</item>
        <item name="android:scaleType">center</item>
    </style>
    <style name="ModernSearchInputStyle" parent="">
        <item name="android:background">@drawable/modern_search_input_background</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textColorHint">@color/black</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>
    <style name="NavigationDrawerItemShape">
        <item name="cornerSize">8dp</item>
        <item name="cornerFamily">rounded</item>
    </style>
    <style name="OrderButtonStyle" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textSize">@dimen/text_size_micro</item>
        <item name="android:padding">@dimen/margin_extra_small</item>
        <item name="cornerRadius">@dimen/button_corner_radius</item>
        <item name="android:minHeight">36dp</item>
    </style>
    <style name="OrderCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">@dimen/card_corner_radius</item>
        <item name="cardElevation">@dimen/elevation_card</item>
        <item name="cardBackgroundColor">@color/white</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">true</item>
    </style>
    <style name="OrderTrackButtonStyle" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textSize">@dimen/text_size_micro</item>
        <item name="android:padding">@dimen/margin_extra_small</item>
        <item name="cornerRadius">@dimen/button_corner_radius</item>
        <item name="android:minHeight">36dp</item>
    </style>
    <style name="PriceTextStyle">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:background">@drawable/price_background</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingBottom">6dp</item>
    </style>
    <style name="PromoDialogTheme" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.7</item>
    </style>
    <style name="RoundedImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearance.App.LargeComponent" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>
    <style name="ShapeAppearance.App.MediumComponent" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>
    <style name="ShapeAppearance.App.SmallComponent" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="textAllCaps">false</item>
    </style>
    <style name="TextAppearance.App.CollapsingToolbar.Collapsed" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.App.CollapsingToolbar.Expanded" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="Theme.GoGoLaundry" parent="Base.Theme.GoGoLaundry"/>
    <style name="ThemeOverlay_App_DatePicker" parent="@style/ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="colorPrimary">@color/picker_gradient_end</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/picker_gradient_start</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSurface">@color/picker_background_overlay</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/surface_elevated_tint</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>
        <item name="materialCalendarHeaderTitle">@style/Widget.App.DatePicker.HeaderTitle</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.App.DatePicker.HeaderSelection</item>
        <item name="materialCalendarHeaderToggleButton">@style/Widget.App.DatePicker.HeaderToggleButton</item>
        <item name="materialCalendarDay">@style/Widget.App.DatePicker.Day</item>
        <item name="materialCalendarStyle">@style/Widget.App.DatePicker</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.App.DatePicker.Button</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.App.DatePicker.Button</item>
        <item name="materialCalendarHeaderDivider">@style/Widget.App.DatePicker.HeaderDivider</item>
        <item name="android:windowBackground">@color/picker_background_overlay</item>
    </style>
    <style name="ThemeOverlay_App_TimePicker" parent="@style/ThemeOverlay.MaterialComponents.TimePicker">
        <item name="colorPrimary">@color/picker_gradient_end</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/picker_gradient_start</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSurface">@color/picker_background_overlay</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/surface_elevated_tint</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>
        <item name="materialClockStyle">@style/Widget.App.TimePicker.Clock</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.App.TimePicker.Button</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.App.TimePicker.Button</item>
        <item name="chipStyle">@style/Widget.App.TimePicker.Chip</item>
        <item name="materialTimePickerStyle">@style/Widget.App.TimePicker</item>
        <item name="android:windowBackground">@color/picker_background_overlay</item>
    </style>
    <style name="TransparentDialog" parent="Theme.Material3.DayNight.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style>
    <style name="Widget.App.DatePicker" parent="Widget.MaterialComponents.MaterialCalendar">
        <item name="android:padding">16dp</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.LargeComponent</item>
        <item name="android:background">@color/picker_background_overlay</item>
        <item name="android:elevation">8dp</item>
    </style>
    <style name="Widget.App.DatePicker.Button" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/picker_gradient_end</item>
        <item name="rippleColor">@color/picker_ripple_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:letterSpacing">0.02</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.SmallComponent</item>
    </style>
    <style name="Widget.App.DatePicker.Day" parent="Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemShapeAppearance">@style/ShapeAppearance.App.SmallComponent</item>
        <item name="itemTextColor">@color/picker_unselected_text</item>
        <item name="itemStrokeColor">@color/picker_border_color</item>
        <item name="itemStrokeWidth">1dp</item>
        <item name="itemFillColor">@color/picker_selected_background</item>
        <item name="itemRippleColor">@color/picker_ripple_color</item>
    </style>
    <style name="Widget.App.DatePicker.HeaderDivider">
        <item name="android:background">@color/picker_border_color</item>
        <item name="android:layout_height">1dp</item>
    </style>
    <style name="Widget.App.DatePicker.HeaderSelection" parent="Widget.MaterialComponents.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?attr/textAppearanceHeadline4</item>
        <item name="android:textColor">@color/picker_gradient_end</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.01</item>
    </style>
    <style name="Widget.App.DatePicker.HeaderTitle" parent="Widget.MaterialComponents.MaterialCalendar.HeaderTitle">
        <item name="android:textAppearance">?attr/textAppearanceHeadline6</item>
        <item name="android:textColor">@color/picker_gradient_end</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.02</item>
    </style>
    <style name="Widget.App.DatePicker.HeaderToggleButton" parent="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:tint">@color/picker_gradient_end</item>
        <item name="backgroundTint">@color/picker_selected_background</item>
        <item name="rippleColor">@color/picker_ripple_color</item>
    </style>
    <style name="Widget.App.TimePicker" parent="Widget.MaterialComponents.TimePicker">
        <item name="android:padding">16dp</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.LargeComponent</item>
        <item name="android:background">@color/picker_background_overlay</item>
        <item name="android:elevation">8dp</item>
    </style>
    <style name="Widget.App.TimePicker.Button" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/picker_gradient_end</item>
        <item name="rippleColor">@color/picker_ripple_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:letterSpacing">0.02</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.SmallComponent</item>
    </style>
    <style name="Widget.App.TimePicker.Chip" parent="Widget.MaterialComponents.TimePicker.Display.TextInputEditText">
        <item name="android:textColor">@color/picker_gradient_end</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@color/picker_selected_background</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.SmallComponent</item>
        <item name="android:letterSpacing">0.05</item>
    </style>
    <style name="Widget.App.TimePicker.Clock" parent="Widget.MaterialComponents.TimePicker.Clock">
        <item name="clockFaceBackgroundColor">@color/picker_selected_background</item>
        <item name="clockHandColor">@color/picker_gradient_end</item>
        <item name="clockNumberTextColor">@color/picker_unselected_text</item>
        <item name="android:background">@color/picker_background_overlay</item>
    </style>
</resources>