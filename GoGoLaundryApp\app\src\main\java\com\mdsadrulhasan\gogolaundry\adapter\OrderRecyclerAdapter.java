package com.mdsadrulhasan.gogolaundry.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.model.Order;

import java.util.List;

/**
 * Adapter for displaying orders in a RecyclerView
 */
public class OrderRecyclerAdapter extends RecyclerView.Adapter<OrderRecyclerAdapter.OrderViewHolder> {

    private final Context context;
    private List<Order> orders;
    private final OrderActionListener listener;

    /**
     * Interface for handling order actions
     */
    public interface OrderActionListener {
        void onOrderClicked(Order order);
        void onTrackOrderClicked(Order order);
        void onReorderClicked(Order order);
        void onCancelOrderClicked(Order order);
    }

    /**
     * Constructor
     *
     * @param context Context
     * @param orders List of orders
     * @param listener Action listener
     */
    public OrderRecyclerAdapter(Context context, List<Order> orders, OrderActionListener listener) {
        this.context = context;
        this.orders = orders;
        this.listener = listener;
    }

    /**
     * Update orders list and refresh adapter
     *
     * @param orders New list of orders
     */
    public void updateOrders(List<Order> orders) {
        this.orders = orders;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public OrderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recent_order, parent, false);
        return new OrderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull OrderViewHolder holder, int position) {
        // Get order at position
        Order order = orders.get(position);

        // Bind data to views
        holder.orderId.setText(context.getString(R.string.order_id, order.getOrderNumber()));
        holder.orderDate.setText(formatOrderDate(order.getCreatedAt()));
        holder.orderStatus.setText(getFormattedStatus(order.getStatus()));
        holder.orderTotal.setText(context.getString(R.string.currency_amount, String.valueOf(order.getTotalAmount())));

        // Set order items info
        if (holder.orderItems != null) {
            String itemsText = getOrderItemsText(order);
            holder.orderItems.setText(itemsText);
        }

        // Set status color and icon
        setStatusStyling(holder, order.getStatus());

        // Set button visibility based on order status
        setButtonVisibility(holder, order.getStatus());

        // Set click listeners
        holder.itemView.setOnClickListener(v -> listener.onOrderClicked(order));
        holder.btnTrackOrder.setOnClickListener(v -> listener.onTrackOrderClicked(order));

        // Use receipt button as reorder button
        if (holder.btnReceipt != null) {
            holder.btnReceipt.setOnClickListener(v -> listener.onReorderClicked(order));
        }

        // Add long press listener for cancel option
        holder.itemView.setOnLongClickListener(v -> {
            if (canCancel(order.getStatus())) {
                listener.onCancelOrderClicked(order);
                return true;
            }
            return false;
        });
    }

    @Override
    public int getItemCount() {
        return orders != null ? orders.size() : 0;
    }

    /**
     * ViewHolder for order items
     */
    static class OrderViewHolder extends RecyclerView.ViewHolder {
        TextView orderId;
        TextView orderDate;
        TextView orderStatus;
        TextView orderTotal;
        TextView orderItems;
        MaterialButton btnTrackOrder;
        MaterialButton btnReceipt;

        public OrderViewHolder(@NonNull View itemView) {
            super(itemView);
            orderId = itemView.findViewById(R.id.order_number);
            orderDate = itemView.findViewById(R.id.order_date);
            orderStatus = itemView.findViewById(R.id.order_status);
            orderTotal = itemView.findViewById(R.id.order_total);
            orderItems = itemView.findViewById(R.id.order_items);
            btnTrackOrder = itemView.findViewById(R.id.btn_track_order);
            btnReceipt = itemView.findViewById(R.id.btn_receipt);
        }
    }

    /**
     * Set status styling (color and icon) based on order status
     *
     * @param holder ViewHolder
     * @param status Order status
     */
    private void setStatusStyling(OrderViewHolder holder, String status) {
        int colorResId;

        switch (status.toLowerCase()) {
            case "placed":
            case "confirmed":
                colorResId = R.color.info;
                break;
            case "picked_up":
            case "processing":
                colorResId = R.color.warning;
                break;
            case "ready":
                colorResId = R.color.primary;
                break;
            case "delivered":
                colorResId = R.color.success;
                break;
            case "cancelled":
                colorResId = R.color.error;
                break;
            default:
                colorResId = R.color.text_secondary;
                break;
        }

        holder.orderStatus.setTextColor(context.getResources().getColor(colorResId));
    }

    /**
     * Set button visibility based on order status
     *
     * @param holder ViewHolder
     * @param status Order status
     */
    private void setButtonVisibility(OrderViewHolder holder, String status) {
        boolean canTrack = !status.equalsIgnoreCase("cancelled") &&
                          !status.equalsIgnoreCase("delivered");

        if (holder.btnTrackOrder != null) {
            holder.btnTrackOrder.setVisibility(canTrack ? View.VISIBLE : View.GONE);
        }

        if (holder.btnReceipt != null) {
            holder.btnReceipt.setVisibility(View.VISIBLE); // Always show receipt/reorder option
        }
    }

    /**
     * Check if order can be cancelled
     *
     * @param status Order status
     * @return True if order can be cancelled
     */
    private boolean canCancel(String status) {
        return status.equalsIgnoreCase("placed") ||
               status.equalsIgnoreCase("confirmed");
    }

    /**
     * Get formatted status text
     *
     * @param status Raw status
     * @return Formatted status
     */
    private String getFormattedStatus(String status) {
        switch (status.toLowerCase()) {
            case "placed":
                return context.getString(R.string.order_placed);
            case "confirmed":
                return context.getString(R.string.order_confirmed);
            case "picked_up":
                return context.getString(R.string.order_picked_up);
            case "processing":
                return context.getString(R.string.order_processing);
            case "ready":
                return context.getString(R.string.order_ready);
            case "delivered":
                return context.getString(R.string.order_delivered);
            case "cancelled":
                return context.getString(R.string.order_cancelled);
            default:
                return status;
        }
    }

    /**
     * Format order date for display
     *
     * @param dateString Raw date string
     * @return Formatted date
     */
    private String formatOrderDate(String dateString) {
        // For now, return the date as is. You can implement proper date formatting here
        return dateString;
    }

    /**
     * Get order items text for display
     *
     * @param order Order object
     * @return Formatted items text
     */
    private String getOrderItemsText(Order order) {
        // Create a simple items description
        // You can enhance this based on your Order model structure
        int itemCount = order.getOrderItems() != null ? order.getOrderItems().size() : 0;
        if (itemCount > 0) {
            return itemCount + " items • Wash & Fold";
        } else {
            return "Items • Wash & Fold";
        }
    }
}
