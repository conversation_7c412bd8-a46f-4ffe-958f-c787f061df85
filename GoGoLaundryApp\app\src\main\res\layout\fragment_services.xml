<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background">

    <!-- <PERSON>er -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="1dp"
        android:layout_marginTop="0dp"
        android:layout_marginEnd="1dp"
        android:background="@drawable/glass_hero_background"
        android:elevation="8dp"
        android:paddingStart="@dimen/margin_medium"
        android:paddingTop="5dp"
        android:paddingEnd="@dimen/margin_medium"
        android:paddingBottom="@dimen/margin_large"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0">

        <TextView
            android:id="@+id/services_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:alpha="0.9"
            android:fontFamily="sans-serif"
            android:letterSpacing="0.01"
            android:text="@string/services_subtitle"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_small"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Glass Content Container -->
    <FrameLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        android:background="@drawable/glass_content_background"
        android:elevation="6dp"
        android:padding="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/header_layout"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/services_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingStart="@dimen/margin_small"
                android:paddingEnd="@dimen/margin_small"
                android:paddingTop="@dimen/margin_medium"
                android:paddingBottom="@dimen/margin_large"
                android:overScrollMode="never"
                android:scrollbarStyle="outsideOverlay"
                android:scrollbars="vertical"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"
                tools:listitem="@layout/item_service_card" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </FrameLayout>

    <!-- Loading and Empty States -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/progress_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_margin="@dimen/margin_medium"
            android:indeterminateTint="@color/primary" />
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/empty_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_large"
        android:visibility="gone"
        app:cardCornerRadius="20dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeColor="@color/divider"
        app:strokeWidth="1dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="@dimen/margin_large">

            <ImageView
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:alpha="0.7"
                android:contentDescription="@string/no_services_available"
                android:src="@drawable/ic_empty_services" />

            <TextView
                android:id="@+id/empty_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_medium"
                android:fontFamily="sans-serif-medium"
                android:gravity="center"
                android:text="@string/no_services_available"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
