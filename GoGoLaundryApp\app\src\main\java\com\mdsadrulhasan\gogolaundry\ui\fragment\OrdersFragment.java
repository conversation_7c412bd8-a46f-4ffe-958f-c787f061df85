package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.google.android.material.button.MaterialButton;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.OrderRecyclerAdapter;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.utils.DialogUtils;
import com.mdsadrulhasan.gogolaundry.utils.OrderTrackingManager;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.OrdersViewModel;
import com.mdsadrulhasan.gogolaundry.ui.fragment.OrderTrackingFragment;

import cn.pedant.SweetAlert.SweetAlertDialog;
import org.json.JSONObject;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for displaying user orders in a list layout
 */
public class OrdersFragment extends Fragment implements OrderRecyclerAdapter.OrderActionListener {

    private OrdersViewModel viewModel;
    private RecyclerView recyclerView;
    private OrderRecyclerAdapter adapter;
    private ProgressBar progressBar;
    private ConstraintLayout emptyView;
    private SwipeRefreshLayout swipeRefreshLayout;

    // Empty state buttons
    private MaterialButton btnStartFirstOrder;
    private MaterialButton btnBrowseServices;

    // Flag to indicate if we should scroll to the top when orders are loaded
    private boolean scrollToTopOnLoad = true;

    /**
     * Create a new instance of OrdersFragment that will highlight the newest order
     *
     * @return A new instance of OrdersFragment
     */
    public static OrdersFragment newInstanceWithHighlight() {
        OrdersFragment fragment = new OrdersFragment();
        fragment.scrollToTopOnLoad = true;
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        viewModel = new ViewModelProvider(this).get(OrdersViewModel.class);

        // Set this fragment to handle configuration changes
        setRetainInstance(true);

        // Check if we should scroll to top from arguments
        if (getArguments() != null) {
            scrollToTopOnLoad = getArguments().getBoolean("scroll_to_top", scrollToTopOnLoad);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_orders, container, false);

        // Initialize views
        recyclerView = view.findViewById(R.id.orders_recycler_view);
        progressBar = view.findViewById(R.id.progress_bar);
        emptyView = view.findViewById(R.id.empty_view);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);

        // Initialize empty state buttons
        btnStartFirstOrder = view.findViewById(R.id.btn_start_first_order);
        btnBrowseServices = view.findViewById(R.id.btn_browse_services);

        // Set up RecyclerView
        setupRecyclerView();

        // Set up empty state button listeners
        setupEmptyStateButtons();

        // Set up SwipeRefreshLayout
        swipeRefreshLayout.setOnRefreshListener(() -> {
            viewModel.refreshOrders();
        });

        return view;
    }

    /**
     * Set up RecyclerView with appropriate adapter and configuration
     */
    private void setupRecyclerView() {
        if (getContext() == null) return;

        // Create adapter if not already created
        if (adapter == null) {
            adapter = new OrderRecyclerAdapter(getContext(), new ArrayList<>(), this);
        }

        // Set layout manager and adapter
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.setAdapter(adapter);

        // Add some spacing between items
        int spacing = getResources().getDimensionPixelSize(R.dimen.margin_small);
        recyclerView.addItemDecoration(new SpacingItemDecoration(spacing));
    }

    /**
     * Set up empty state button listeners
     */
    private void setupEmptyStateButtons() {
        if (btnStartFirstOrder != null) {
            btnStartFirstOrder.setOnClickListener(v -> {
                // Navigate to services fragment to start first order
                navigateToServices();
            });
        }

        if (btnBrowseServices != null) {
            btnBrowseServices.setOnClickListener(v -> {
                // Navigate to services fragment to browse
                navigateToServices();
            });
        }
    }

    /**
     * Navigate to Services fragment
     */
    private void navigateToServices() {
        if (getActivity() == null) {
            Log.w("OrdersFragment", "Activity is null, cannot navigate to services");
            return;
        }

        try {
            // Import the ServicesFragment
            com.mdsadrulhasan.gogolaundry.ui.fragment.ServicesFragment servicesFragment =
                new com.mdsadrulhasan.gogolaundry.ui.fragment.ServicesFragment();

            // Clear back stack to avoid navigation issues
            getActivity().getSupportFragmentManager().popBackStack(null,
                androidx.fragment.app.FragmentManager.POP_BACK_STACK_INCLUSIVE);

            // Replace current fragment with ServicesFragment
            getActivity().getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                    .replace(R.id.fragment_container, servicesFragment)
                    .commit();

            // Update bottom navigation if MainActivity has the method
            if (getActivity() instanceof com.mdsadrulhasan.gogolaundry.MainActivity) {
                com.mdsadrulhasan.gogolaundry.MainActivity mainActivity =
                    (com.mdsadrulhasan.gogolaundry.MainActivity) getActivity();

                // Update bottom navigation selection to Services
                mainActivity.updateBottomNavigation(R.id.nav_services);
            }

            Log.d("OrdersFragment", "Successfully navigated to Services fragment");
        } catch (Exception e) {
            Log.e("OrdersFragment", "Error navigating to Services fragment: " + e.getMessage());

            // Show error toast as fallback
            if (isAdded() && getContext() != null) {
                Toast.makeText(getContext(), "Error navigating to Services", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * Item decoration for adding spacing between RecyclerView items
     */
    private static class SpacingItemDecoration extends RecyclerView.ItemDecoration {
        private final int spacing;

        public SpacingItemDecoration(int spacing) {
            this.spacing = spacing;
        }

        @Override
        public void getItemOffsets(@NonNull android.graphics.Rect outRect, @NonNull View view,
                                  @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
            outRect.bottom = spacing;
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // Update RecyclerView when orientation changes
        setupRecyclerView();

        // Refresh the adapter to redraw items with new dimensions
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Force refresh orders when fragment is created
        Log.d("OrdersFragment", "Forcing refresh of orders");
        viewModel.refreshOrders();

        // Observe orders data
        viewModel.getOrders().observe(getViewLifecycleOwner(), resource -> {
            Log.d("OrdersFragment", "Orders resource state: " +
                  (resource.isLoading() ? "Loading" :
                   resource.isSuccess() ? "Success" :
                   resource.isError() ? "Error: " + resource.getMessage() : "Unknown"));
            swipeRefreshLayout.setRefreshing(false);

            if (resource.isLoading()) {
                showLoading();
            } else if (resource.isSuccess()) {
                List<OrderEntity> orderEntities = resource.getData();
                if (orderEntities != null && !orderEntities.isEmpty()) {
                    // Convert entities to UI models
                    List<Order> orders = convertToOrderModels(orderEntities);

                    // Log the orders for debugging
                    for (Order order : orders) {
                        Log.d("OrdersFragment", "Order: " + order.getOrderNumber() + ", Status: " + order.getStatus());
                    }

                    // Show all orders
                    showOrders(orders);
                } else {
                    showEmpty();
                }
            } else if (resource.isError()) {
                showError(resource.getMessage());
            }
        });
    }

    /**
     * Convert OrderEntity list to Order model list for UI
     *
     * @param orderEntities List of order entities
     * @return List of order models
     */
    private List<Order> convertToOrderModels(List<OrderEntity> orderEntities) {
        // This is a simple conversion example. In a real app, you might have a more complex mapping
        List<Order> orders = new ArrayList<>();

        for (OrderEntity entity : orderEntities) {
            Order order = new Order();
            order.setId(entity.getId());
            order.setOrderNumber(entity.getOrderNumber());
            order.setTrackingNumber(entity.getTrackingNumber());
            order.setUserId(entity.getUserId());
            order.setTotalAmount(entity.getTotal());
            order.setStatus(entity.getStatus());
            order.setPaymentStatus(entity.getPaymentStatus());
            order.setPaymentMethod(entity.getPaymentMethod());
            order.setCreatedAt(entity.getCreatedAt().toString());
            order.setUpdatedAt(entity.getUpdatedAt().toString());

            // You would also convert order items here if needed
            orders.add(order);
        }

        return orders;
    }

    /**
     * Show loading state
     */
    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);
        emptyView.setVisibility(View.GONE);
    }

    /**
     * Show orders data
     *
     * @param orders List of orders to display
     */
    private void showOrders(List<Order> orders) {
        progressBar.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        emptyView.setVisibility(View.GONE);
        adapter.updateOrders(orders);

        // Scroll to the top if needed (e.g., after placing a new order)
        if (scrollToTopOnLoad && !orders.isEmpty()) {
            recyclerView.post(() -> {
                recyclerView.smoothScrollToPosition(0);

                // Highlight the first item (newest order) with a subtle animation
                if (recyclerView.findViewHolderForAdapterPosition(0) != null) {
                    View itemView = recyclerView.findViewHolderForAdapterPosition(0).itemView;
                    highlightNewOrder(itemView);
                }
            });

            // Reset the flag after scrolling
            scrollToTopOnLoad = false;
        }
    }

    /**
     * Highlight a newly placed order with a subtle animation
     *
     * @param view The view to highlight
     */
    private void highlightNewOrder(View view) {
        if (view == null) return;

        try {
            // Check if fragment is attached before proceeding
            if (!isAdded()) {
                Log.w("OrdersFragment", "Fragment not attached when trying to highlight order");
                return;
            }

            // Store colors before starting animations to avoid resource access in callbacks
            int primaryLightColor = 0;
            int transparentColor = 0;

            try {
                // Get colors safely
                primaryLightColor = getResources().getColor(R.color.primary_light);
                transparentColor = getResources().getColor(android.R.color.transparent);
            } catch (Exception e) {
                Log.e("OrdersFragment", "Error getting colors: " + e.getMessage());
                return;
            }

            // Set initial alpha
            view.setAlpha(0.7f);

            // Set initial background color
            view.setBackgroundColor(primaryLightColor);

            // Create a simple fade-in animation
            view.animate()
                    .alpha(1.0f)
                    .setDuration(1000)
                    .start();

            // Create a delayed runnable to reset the background color
            // This avoids using animation callbacks that might execute after fragment detachment
            final int finalTransparentColor = transparentColor;
            view.postDelayed(() -> {
                try {
                    if (view != null && view.getParent() != null) {
                        view.setBackgroundColor(finalTransparentColor);
                    }
                } catch (Exception e) {
                    Log.e("OrdersFragment", "Error in delayed background reset: " + e.getMessage());
                }
            }, 2000); // 2 seconds total (matches the original animation duration)
        } catch (Exception e) {
            Log.e("OrdersFragment", "Error in highlightNewOrder: " + e.getMessage());
        }
    }

    /**
     * Show empty state
     */
    private void showEmpty() {
        progressBar.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        emptyView.setVisibility(View.VISIBLE);

        // The empty state layout already contains the appropriate message and buttons
        // No need to set text as it's now a ConstraintLayout with predefined content
    }

    /**
     * Show error state
     *
     * @param message Error message
     */
    private void showError(String message) {
        // Check if fragment is still attached
        if (!isAdded() || getContext() == null) {
            Log.w("OrdersFragment", "Fragment not attached when trying to show error: " + message);
            return;
        }

        try {
            progressBar.setVisibility(View.GONE);

            // Show toast with error message
            try {
                Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                Log.e("OrdersFragment", "Error showing toast: " + e.getMessage());
            }

            // If adapter is empty, show empty view (which will show the beautiful empty state)
            if (adapter != null && adapter.getItemCount() == 0) {
                recyclerView.setVisibility(View.GONE);
                emptyView.setVisibility(View.VISIBLE);
                // The empty state layout will handle the display
                // Error message is already shown via toast
            }
        } catch (Exception e) {
            Log.e("OrdersFragment", "Error showing error state: " + e.getMessage());
        }
    }

    @Override
    public void onOrderClicked(Order order) {
        // Navigate to order details
        Toast.makeText(getContext(), "Order clicked: " + order.getOrderNumber(), Toast.LENGTH_SHORT).show();

        // TODO: Implement navigation to order details
    }

    @Override
    public void onTrackOrderClicked(Order order) {
        // Check if fragment is still attached
        if (!isAdded() || getActivity() == null) {
            Log.w("OrdersFragment", "Fragment not attached when trying to track order: " + order.getOrderNumber());
            return;
        }

        try {
            // Get tracking number
            String trackingNumber = order.getTrackingNumber();
            if (trackingNumber == null || trackingNumber.isEmpty()) {
                // If tracking number is not available, use order number
                trackingNumber = order.getOrderNumber();
            }

            // Create and navigate to OrderTrackingFragment
            OrderTrackingFragment trackingFragment = OrderTrackingFragment.newInstance(
                    trackingNumber, order.getOrderNumber());

            // Replace current fragment with OrderTrackingFragment
            getActivity().getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_container, trackingFragment)
                    .addToBackStack(null)
                    .commit();
        } catch (Exception e) {
            Log.e("OrdersFragment", "Error navigating to tracking fragment: " + e.getMessage());

            // Show error toast if possible
            if (isAdded() && getContext() != null) {
                try {
                    Toast.makeText(getContext(), "Error tracking order: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                } catch (Exception ex) {
                    Log.e("OrdersFragment", "Error showing toast: " + ex.getMessage());
                }
            }
        }
    }

    /**
     * Format order status message for display
     *
     * @param order Order with status information
     * @return Formatted status message
     */
    private String getFormattedStatusMessage(Order order) {
        StringBuilder message = new StringBuilder();
        message.append("Order #").append(order.getOrderNumber()).append("\n\n");
        message.append("Status: ").append(formatStatus(order.getStatus())).append("\n");

        if (order.getPickupDate() != null) {
            message.append("Pickup: ").append(order.getPickupDate());
            if (order.getPickupTime() != null) {
                message.append(" ").append(order.getPickupTime());
            }
            message.append("\n");
        }

        if (order.getDeliveryDate() != null) {
            message.append("Delivery: ").append(order.getDeliveryDate());
            if (order.getDeliveryTime() != null) {
                message.append(" ").append(order.getDeliveryTime());
            }
            message.append("\n");
        }

        message.append("\nTotal: ₹").append(order.getTotalAmount());

        return message.toString();
    }

    /**
     * Format status string for display
     *
     * @param status Raw status string
     * @return Formatted status string
     */
    private String formatStatus(String status) {
        if (status == null) return "Unknown";

        // Convert snake_case to Title Case
        String[] words = status.split("_");
        StringBuilder formatted = new StringBuilder();

        for (String word : words) {
            if (word.length() > 0) {
                formatted.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    formatted.append(word.substring(1).toLowerCase());
                }
                formatted.append(" ");
            }
        }

        return formatted.toString().trim();
    }

    @Override
    public void onReorderClicked(Order order) {
        // Show loading dialog
        SweetAlertDialog loadingDialog = DialogUtils.showLoadingDialog(getContext(), "Processing your order...");

        // Set a timeout for the loading dialog (10 seconds)
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // If the dialog is still showing after 10 seconds, dismiss it and show an error
            if (loadingDialog != null && loadingDialog.isShowing()) {
                try {
                    loadingDialog.dismissWithAnimation();
                    DialogUtils.showErrorDialog(
                            getContext(),
                            "Request Timeout",
                            "The request is taking too long. Please try again later.");
                } catch (Exception e) {
                    Log.e("OrdersFragment", "Error dismissing dialog: " + e.getMessage());
                }
            }
        }, 10000); // 10 seconds timeout

        // Get user ID from session
        SessionManager sessionManager = new SessionManager(getContext());
        int userId = sessionManager.getUser().getId();

        // Make API call to reorder
        ApiService apiService = ApiClient.getApiService(getContext());
        Call<ApiResponse<Order>> call = apiService.reorderExistingOrder(order.getId(), userId);
        call.enqueue(new Callback<ApiResponse<Order>>() {
            @Override
            public void onResponse(Call<ApiResponse<Order>> call, Response<ApiResponse<Order>> response) {
                // Dismiss loading dialog
                if (loadingDialog != null && loadingDialog.isShowing()) {
                    try {
                        loadingDialog.dismissWithAnimation();
                    } catch (Exception e) {
                        Log.e("OrdersFragment", "Error dismissing dialog: " + e.getMessage());
                    }
                }

                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    // Reorder successful
                    Order newOrder = response.body().getData();

                    // Show success toast
                    ToastUtils.showReorderToast(getContext(), order.getOrderNumber());

                    // Refresh orders list
                    viewModel.refreshOrders();

                    // Show success dialog with more details
                    DialogUtils.showSuccessDialog(
                            getContext(),
                            "Reorder Successful",
                            "Your order has been placed successfully. New order number: " + newOrder.getOrderNumber(),
                            "View Details",
                            sweetAlertDialog -> {
                                // Navigate to order details
                                // TODO: Implement navigation to order details
                                sweetAlertDialog.dismissWithAnimation();
                            });
                } else {
                    // Reorder failed
                    String errorMessage = "Failed to reorder";
                    if (response.body() != null) {
                        errorMessage = response.body().getMessage();
                    } else if (response.errorBody() != null) {
                        try {
                            String errorBody = response.errorBody().string();
                            JSONObject errorJson = new JSONObject(errorBody);
                            errorMessage = errorJson.optString("message", "Failed to reorder");
                        } catch (Exception e) {
                            Log.e("OrdersFragment", "Error parsing error body: " + e.getMessage());
                        }
                    }

                    // Show error dialog instead of just a toast
                    DialogUtils.showErrorDialog(
                            getContext(),
                            "Reorder Failed",
                            errorMessage);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Order>> call, Throwable t) {
                // Dismiss loading dialog
                if (loadingDialog != null && loadingDialog.isShowing()) {
                    try {
                        loadingDialog.dismissWithAnimation();
                    } catch (Exception e) {
                        Log.e("OrdersFragment", "Error dismissing dialog: " + e.getMessage());
                    }
                }

                // Show error dialog
                String errorMessage = "Network error";
                if (t.getMessage() != null) {
                    if (t.getMessage().contains("Unable to resolve host")) {
                        errorMessage = "No internet connection. Please check your network settings.";
                    } else if (t.getMessage().contains("timeout")) {
                        errorMessage = "Request timed out. Please try again.";
                    } else {
                        errorMessage = t.getMessage();
                    }
                }

                // Show error dialog instead of just a toast
                DialogUtils.showErrorDialog(
                        getContext(),
                        "Reorder Failed",
                        errorMessage);
            }
        });
    }

    @Override
    public void onCancelOrderClicked(Order order) {
        // Cancel order
        Toast.makeText(getContext(), "Cancelling order: " + order.getOrderNumber(), Toast.LENGTH_SHORT).show();

        // For now, just refresh the orders
        // In a real app, you would call an API to cancel the order
        viewModel.refreshOrders();

        // TODO: Implement order cancellation functionality
    }
}
